@extends('layouts.app')

@section('title', 'Donasi Management')
@section('panel-type', 'Donasi Panel')

@section('content')
<div x-data="donasiManagement()"
     x-cloak
     @view-donasi.window="viewDonasi($event.detail.id)"
     @edit-donasi.window="editDonasi($event.detail.id)"
     @delete-donasi.window="confirmDelete($event.detail.id)">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Donasi Management</h2>
        @if(auth()->user()->isAdmin())
        <button @click="openCreateDialog()" class="bg-blue-500 text-white text-sm px-4 py-2 rounded-lg hover:bg-blue-600 cursor-pointer">
            New
        </button>
        @endif
    </div>

    <!-- Filter Form -->
    @include('dashboard.donasi.filter')

    <!-- Donasi Table with Loading Overlay -->
    <div class="relative">
        <div x-show="loading"
             class="absolute inset-0 backdrop-blur-xs flex items-center justify-center z-10">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="text-gray-500 text-sm">Loading...</span>
            </div>
        </div>
        <div id="donasi-table-container" x-ref="donasiTableContainer">
            <div id="donasi-table">
                @include('dashboard.donasi.table', ['donasi' => $donasi ?? $donations ?? []])
            </div>
            @php $paginator = $donasi ?? $donations ?? null; @endphp
            @if($paginator instanceof \Illuminate\Pagination\LengthAwarePaginator)
                <div class="mt-1 px-4 py-3 rounded-b-lg" id="pagination-links">
                    <x-pagination :paginator="$paginator" />
                </div>
            @endif
        </div>
    </div>

    <!-- Create/Edit Dialog -->
    @include('dashboard.donasi.form')

    <!-- View Dialog -->
    @include('dashboard.donasi.view')

    <!-- Delete Confirmation Modal -->
    <x-delete-confirmation 
        showVariable="showDeleteDialog"
        title="Confirm Deletion"
        message="Are you sure you want to delete this donasi? This action cannot be undone."
        deleteMethod="deleteDonasi()"
        cancelMethod="showDeleteDialog = false"
    />
</div>
@endsection

@push('scripts')
<script>
function donasiManagement() {
    return {
        showDialog: false,
        showViewDialog: false,
        showDeleteDialog: false,
        formData: {
            amount: '',
            donatur: '',
            program: '',
            status: 'pending',
            penghubung: '',
            image_url: null,
            image_path: null,
            is_webhook: false,
            moota: null
        },
        selectedFile: null,
        errors: {},
        viewData: {
            image_url: null,
            donor_name: '',
            amount: 0,
            program_name: '',
            penghubung_name: '',
            manager_name: '',
            status: '',
            created_at: '',
            moota: null
        },
        selectedDonatur: {
            name: ''
        },
        dialogTitle: '',
        loading: false,
        currentDonasiId: null,
        processingPenghubung: false,
        filters: {
            donatur: '',
            program: '',
            penghubung: '',
            manager: '',
            creation: ''
        },
        page: 1,
        perPage: 10,
        lastPage: 1,
        currentPageUrl: '/dashboard/donasi/table',
        openCreateDialog() {
            this.dialogTitle = 'New Donasi';
            // Reset form data
            this.formData = {
                amount: '',
                donatur: '',
                program: '',
                status: 'pending',
                penghubung: '',
                image_url: null,
                image_path: null,
                is_webhook: false
            };
            this.currentDonasiId = null;
            this.errors = {};

            // Reset form components
            this.$nextTick(() => {
                const donaturComponent = document.getElementById('donatur-form-input');
                if (donaturComponent) {
                    donaturComponent.dispatchEvent(new CustomEvent('reset-form'));
                }

                const programComponent = document.getElementById('program-form-input');
                if (programComponent) {
                    programComponent.dispatchEvent(new CustomEvent('reset-form'));
                }

                const penghubungComponent = document.getElementById('penghubung-form-input');
                if (penghubungComponent) {
                    penghubungComponent.dispatchEvent(new CustomEvent('reset-form'));
                }
            });

            this.showDialog = true;
        },
        closeDialog() {
            this.showDialog = false;
        },
        async viewDonasi(id) {
            this.currentDonasiId = id;

            try {
                const response = await fetch(`/donations/show/${id}`);
                const data = await response.json();

                // Populate view data
                this.viewData = {
                    image_url: data.proof_image,
                    donor_name: data.donatur ? data.donatur.name : '-',
                    amount: data.moota ? data.moota.amount : 0,
                    program_name: data.program ? data.program.name : '-',
                    penghubung_name: data.staff ? data.staff.name : '-',
                    manager_name: data.staff && data.staff.manager ? data.staff.manager : '-',
                    status: 'pending', // Default status since it's not in the donations table
                    created_at: data.moota && data.moota.date ? data.moota.date : data.created_at,
                    moota: data.moota
                };

                this.showViewDialog = true;
            } catch (error) {
                console.error('Error loading donation data:', error);
                alert('Error loading donation data');
            }
        },
        async editDonasi(id) {
            this.currentDonasiId = id;
            this.dialogTitle = 'Edit Donasi';

            try {
                const response = await fetch(`/donations/edit/${id}`);
                const data = await response.json();

                // Populate form data
                this.formData = {
                    id: data.id,
                    amount: data.moota ? data.moota.amount : 0,
                    donatur: data.donatur ? { id: data.donatur.id, name: data.donatur.name } : '',
                    program: data.program ? { id: data.program.id, name: data.program.name } : '',
                    status: 'pending',
                    penghubung: data.staff ? { id: data.staff.id, name: data.staff.name } : '',
                    image_url: null,
                    image_path: null,
                    is_webhook: data.moota && data.moota.creation === 'webhook',
                    moota: data.moota
                };

                this.showDialog = true;

                // Initialize form components with data after dialog is shown
                this.$nextTick(() => {
                    // Initialize donatur component
                    if (data.donatur) {
                        const donaturComponent = document.getElementById('donatur-form-input');
                        if (donaturComponent) {
                            donaturComponent.dispatchEvent(new CustomEvent('set-selected-donatur', {
                                detail: { donatur: data.donatur }
                            }));
                        }
                    }

                    // Initialize program component
                    if (data.program) {
                        const programComponent = document.getElementById('program-form-input');
                        if (programComponent) {
                            programComponent.dispatchEvent(new CustomEvent('set-selected-programs', {
                                detail: { programs: [data.program] }
                            }));
                        }
                    }

                    // Initialize penghubung component
                    if (data.staff) {
                        const penghubungComponent = document.getElementById('penghubung-form-input');
                        if (penghubungComponent) {
                            penghubungComponent.dispatchEvent(new CustomEvent('set-selected-user', {
                                detail: { user: data.staff }
                            }));
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading donation data:', error);
                alert('Error loading donation data');
            }
        },
        confirmDelete(id) {
            this.currentDonasiId = id;
            this.showDeleteDialog = true;
        },

        validateForm() {
            this.errors = {};
            let isValid = true;

            if (!this.formData.amount || this.formData.amount <= 0) {
                this.errors.amount = 'Amount is required and must be greater than 0';
                isValid = false;
            }

            return isValid;
        },

        async handlePenghubungAction() {
            this.processingPenghubung = true;
            try {
                // Add your penghubung action logic here
                console.log('Penghubung action triggered');

                // Example: You could add functionality like:
                // - Clear current penghubung selection
                // - Auto-assign current user as penghubung
                // - Open a special penghubung selection dialog
                // - etc.

                // For now, just a placeholder
                alert('Penghubung action - implement your desired functionality here');

            } catch (error) {
                console.error('Error in penghubung action:', error);
                alert('Error processing penghubung action');
            } finally {
                this.processingPenghubung = false;
            }
        },

        async submitForm() {
            // Validate form before submission
            if (!this.validateForm()) {
                return;
            }

            this.loading = true;
            this.errors = {};

            try {
                let url = '/donations';
                let method = 'POST';

                if (this.currentDonasiId) {
                    url = `/donations/${this.currentDonasiId}`;
                    method = 'PUT';
                }

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: JSON.stringify(this.formData)
                });

                const result = await response.json();

                if (!response.ok) {
                    if (response.status === 422) {
                        this.errors = result.errors;
                    } else {
                        throw new Error(result.message || 'An error occurred');
                    }
                } else {
                    this.closeDialog();
                    this.loadPage(this.currentPageUrl || '/dashboard/donasi/table');
                }
            } catch (error) {
                console.error('Error submitting form:', error);
                alert('Error submitting form: ' + error.message);
            } finally {
                this.loading = false;
            }
        },

        async deleteDonasi() {
            try {
                this.loading = true;

                const response = await fetch(`/donations/${this.currentDonasiId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    this.showDeleteDialog = false;
                    this.loadPage(this.currentPageUrl);
                } else {
                    alert(result.message || 'Failed to delete donation');
                }
            } catch (error) {
                console.error('Error deleting donation:', error);
                alert('An error occurred while deleting the donation');
            } finally {
                this.loading = false;
            }
        },
        applyFilters() {
            const url = new URL('/dashboard/donasi/table', window.location.origin);

            // Add filter parameters if they have values
            if (this.filters.donatur) url.searchParams.set('donatur', this.filters.donatur);
            if (this.filters.program) url.searchParams.set('program', this.filters.program);
            if (this.filters.penghubung) url.searchParams.set('penghubung', this.filters.penghubung);
            if (this.filters.manager) url.searchParams.set('manager', this.filters.manager);
            if (this.filters.creation) url.searchParams.set('creation', this.filters.creation);

            this.loadPage(url.toString());
        },

        async loadPage(url) {
            try {
                this.loading = true;
                // Store the current page URL
                this.currentPageUrl = url;

                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const html = await response.text();

                if (!document.startViewTransition) {
                    document.getElementById('donasi-table').innerHTML = html;
                    return;
                }

                document.startViewTransition(() => {
                    document.getElementById('donasi-table').innerHTML = html;
                });
            } catch (error) {
                console.error('Error loading page:', error);
            } finally {
                this.loading = false;
            }
        },

        gotoPage(page) {
            if (page < 1 || page > this.lastPage) return;
            const url = new URL('/dashboard/donasi/table', window.location.origin);

            // Add current filter parameters
            if (this.filters.donatur) url.searchParams.set('donatur', this.filters.donatur);
            if (this.filters.program) url.searchParams.set('program', this.filters.program);
            if (this.filters.penghubung) url.searchParams.set('penghubung', this.filters.penghubung);
            if (this.filters.manager) url.searchParams.set('manager', this.filters.manager);
            if (this.filters.creation) url.searchParams.set('creation', this.filters.creation);
            url.searchParams.set('page', page);

            this.loadPage(url.toString());
        },
        setupPagination() {
            document.addEventListener('click', (e) => {
                const element = e.target.closest('#pagination-links a');
                if (element) {
                    e.preventDefault();
                    this.loadPage(element.href);
                }
            });
        },
        async loadPage(url) {
            try {
                this.loading = true;
                this.currentPageUrl = url;
                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const html = await response.text();
                
                // Create a temporary container to parse the HTML response
                const tempContainer = document.createElement('div');
                tempContainer.innerHTML = html;
                
                // Update the table content
                const tableContent = tempContainer.querySelector('#donasi-table');
                if (tableContent) {
                    document.querySelector('#donasi-table').innerHTML = tableContent.innerHTML;
                }
                
                // Update the pagination links
                const paginationLinks = tempContainer.querySelector('#pagination-links');
                if (paginationLinks) {
                    const currentPaginationLinks = document.querySelector('#pagination-links');
                    if (currentPaginationLinks) {
                        currentPaginationLinks.innerHTML = paginationLinks.innerHTML;
                    }
                    
                    const lastPageEl = paginationLinks.querySelector('[data-last-page]');
                    if (lastPageEl) {
                        this.lastPage = parseInt(lastPageEl.getAttribute('data-last-page'), 10);
                    }
                }
                
                // Initialize Alpine.js on the updated content
                Alpine.initTree(document.querySelector('#donasi-table-container'));
            } catch (error) {
                console.error('Error loading page:', error);
            } finally {
                this.loading = false;
            }
        },
        init() {
            this.setupPagination();
            this.currentPageUrl = '/dashboard/donasi/table';

            // Listen for custom filter events if needed
            window.addEventListener('close-all-dialogs', () => {
                this.closeDialog && this.closeDialog();
                this.showViewDialog = false;
                this.showDeleteDialog = false;
            });
        }
    };
}
document.addEventListener('alpine:init', () => {
    Alpine.data('donasiManagement', donasiManagement);
});
</script>
@endpush
